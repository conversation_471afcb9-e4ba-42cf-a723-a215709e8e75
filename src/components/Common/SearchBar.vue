<template>
  <div class="search-container">
    <div class="search-input-wrapper">
      <div class="input-container">
        <input
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="placeholder"
          maxlength="50"
          @input="onSearchInput"
        />
        <button v-show="searchQuery.trim()" class="clear-btn" @click="handleClear">×</button>
      </div>
      <button class="add-person-btn-header" @click="handleAdd">
        <span class="add-icon">+</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Props
interface IProps {
  placeholder?: string;
}

withDefaults(defineProps<IProps>(), {
  placeholder: '请输入姓名进行搜索',
});

// Emits
const emit = defineEmits<{
  search: [query: string];
  add: [];
  input: [query: string];
}>();

// 响应式数据
const searchQuery = ref('');
let searchTimer: NodeJS.Timeout | null = null;

// 处理搜索
const handleSearch = (query: string) => {
  if (query.trim()) {
    emit('search', query.trim());
  }
};

// 处理添加
const handleAdd = () => {
  emit('add');
};

// 处理清空
const handleClear = () => {
  searchQuery.value = '';
  // 清除搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
  // 通知父组件输入变化
  emit('input', '');
};

// 处理输入 - 实现防抖搜索
const onSearchInput = () => {
  emit('input', searchQuery.value);

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，2秒后执行搜索
  searchTimer = setTimeout(() => {
    handleSearch(searchQuery.value);
  }, 1000);
};

// 暴露方法供父组件调用
defineExpose({
  clearSearch: () => {
    searchQuery.value = '';
    // 清除搜索定时器
    if (searchTimer) {
      clearTimeout(searchTimer);
      searchTimer = null;
    }
  },
  getSearchQuery: () => searchQuery.value,
});
</script>

<style lang="scss" scoped>
.search-container {
  padding: 18px 0px;

  .search-input-wrapper {
    display: flex;
    gap: 20px;
    align-items: center;

    .input-container {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;

      .search-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 18px;
        padding: 16px 20px;
        padding-right: 50px; // 为清空按钮留出空间
        color: rgba(255, 255, 255, 0.95);
        font-size: 26px; // 增大字体，原来是22px
        box-sizing: border-box;
        transition: all 0.2s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.5);
          background: rgba(255, 255, 255, 0.15);
        }
      }

      .clear-btn {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .add-person-btn-header {
      padding: 16px 16px;
      border-radius: 50%;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid;
      color: #00bcd4;
      border-color: #00bcd4;
      background: rgba(0, 188, 212, 0.15);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      min-width: 60px;
      height: 58px; // 与search-input高度一致
      box-sizing: border-box;

      .add-icon {
        font-size: 32px;
        line-height: 1;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
</style>
